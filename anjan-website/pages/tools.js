import { useState, useEffect } from "react";
import styled from "styled-components";
import styles from "../styles/Home.module.css";
import { device } from "../components/device";
import Head from "next/head";

const BiggerFlex = styled.div`
  display: flex;
  justify-content: space-between;
  flex-flow: column wrap;
  @media ${device.mobileL} {
    width: 80%;
    margin-top: 20%;
  }

  @media ${device.mobileM} {
    width: 80%;
    margin-top: 20%;
  }

  @media ${device.mobileS} {
    width: 80%;
    margin-top: 20%;
  }

  @media ${device.laptop} {
    width: 65%;
    margin-top: 7.5%;
  }

  @media ${device.desktop} {
    width: 65%;
    margin-top: 7.5%;
  }
`;

const Headline = styled.div`
  font-size: 35px;
  text-align: left;
  margin-bottom: 30px;
  @media ${device.mobileL} {
    font-size: 30px;
  }

  @media ${device.mobileM} {
    font-size: 25px;
  }

  @media ${device.mobileS} {
    font-size: 20px;
  }

  @media ${device.laptop} {
    font-size: 30px;
  }

  @media ${device.desktop} {
    font-size: 30px;
  }
`;

const ToolsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 20px;
`;

const ToolItem = styled.a`
  display: flex;
  flex-direction: column;
  padding: 20px;
  border: 1px solid #eaeaea;
  border-radius: 10px;
  text-decoration: none;
  color: inherit;
  transition: all 0.15s ease;

  &:hover {
    color: #0070f3;
    border-color: #0070f3;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 112, 243, 0.15);
  }
`;

const ToolTitle = styled.h3`
  margin: 0 0 8px 0;
  font-size: 1.5rem;
  font-weight: 600;
`;

const ToolDescription = styled.p`
  margin: 0;
  font-size: 1rem;
  color: #666;
  line-height: 1.5;
`;

const PasswordForm = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  gap: 20px;
`;

const PasswordInput = styled.input`
  padding: 12px 16px;
  font-size: 16px;
  border: 2px solid #eaeaea;
  border-radius: 8px;
  width: 300px;
  max-width: 90%;

  &:focus {
    outline: none;
    border-color: #0070f3;
  }
`;

const PasswordButton = styled.button`
  padding: 12px 24px;
  font-size: 16px;
  background-color: #0070f3;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.15s ease;

  &:hover {
    background-color: #0051a2;
  }

  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }
`;

const ErrorMessage = styled.p`
  color: #e00;
  font-size: 14px;
  margin: 0;
`;

export default function Tools() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(true);

  // Check if user is already authenticated
  useEffect(() => {
    const authStatus = localStorage.getItem("toolsAuth");
    if (authStatus === "true") {
      setIsAuthenticated(true);
    }
    setIsLoading(false);
  }, []);

  const handlePasswordSubmit = async (e) => {
    e.preventDefault();

    try {
      const response = await fetch("/api/verify-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ password }),
      });

      const data = await response.json();

      if (data.success) {
        setIsAuthenticated(true);
        localStorage.setItem("toolsAuth", "true");
        setError("");
      } else {
        setError("Incorrect password");
        setPassword("");
      }
    } catch (error) {
      setError("An error occurred. Please try again.");
      console.error("Error verifying password:", error);
    }
  };

  const handleLogout = () => {
    setIsAuthenticated(false);
    localStorage.removeItem("toolsAuth");
  };

  if (isLoading) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: "100vh",
        }}
      >
        <p>Loading...</p>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <>
        <Head>
          <title>Tools - Password Required</title>
          <meta name="description" content="Private tools access" />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
        </Head>

        <div className={styles.main}>
          <BiggerFlex>
            <PasswordForm>
              <h1 className={styles.title}>🔒 Private Tools</h1>
              <p>Enter password to access private tools</p>
              <form onSubmit={handlePasswordSubmit}>
                <PasswordInput
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter password"
                  autoFocus
                />
                <br />
                <PasswordButton type="submit" disabled={!password}>
                  Access Tools
                </PasswordButton>
              </form>
              {error && <ErrorMessage>{error}</ErrorMessage>}
            </PasswordForm>
          </BiggerFlex>
        </div>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>Private Tools</title>
        <meta name="description" content="Private tools and utilities" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div className={styles.main}>
        <BiggerFlex>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <h1 className={styles.title}>🛠️ Private Tools</h1>
            <PasswordButton
              onClick={handleLogout}
              style={{ fontSize: "14px", padding: "8px 16px" }}
            >
              Logout
            </PasswordButton>
          </div>

          <Headline>Quick access to private parts of the site</Headline>

          <ToolsList>
            <ToolItem href="/surbhi">
              <ToolTitle>Surbhi's Collection</ToolTitle>
              <ToolDescription>girlfriend's card collection</ToolDescription>
            </ToolItem>

            <ToolItem href="/locked">
              <ToolTitle>Lock Reminder</ToolTitle>
              <ToolDescription>Did I lock my door</ToolDescription>
            </ToolItem>
          </ToolsList>
        </BiggerFlex>
      </div>
    </>
  );
}
