import { useState, useEffect } from "react";
import Head from "next/head";

export default function LockReminder() {
  const [isLocked, setIsLocked] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [lockLog, setLockLog] = useState([]);

  // Get current date in YYYY-MM-DD format in PST
  const getCurrentDate = () => {
    const now = new Date();
    const pstDate = new Date(
      now.toLocaleString("en-US", { timeZone: "America/Los_Angeles" })
    );
    return (
      pstDate.getFullYear() +
      "-" +
      String(pstDate.getMonth() + 1).padStart(2, "0") +
      "-" +
      String(pstDate.getDate()).padStart(2, "0")
    );
  };

  // Get current time in PST with readable format
  const getCurrentTimestamp = () => {
    const now = new Date();
    return now.toLocaleString("en-US", {
      timeZone: "America/Los_Angeles",
      hour: "numeric",
      minute: "2-digit",
      second: "2-digit",
      hour12: true,
    });
  };

  // Check lock status and load log on component mount
  useEffect(() => {
    const currentDate = getCurrentDate();
    const savedDate = localStorage.getItem("lockDate");
    const savedLog = localStorage.getItem(`lockLog_${currentDate}`);

    if (savedDate === currentDate) {
      setIsLocked(true);
    } else {
      setIsLocked(false);
      // Clear old date if it's different
      if (savedDate && savedDate !== currentDate) {
        localStorage.removeItem("lockDate");
      }
    }

    // Load today's log or initialize empty array
    if (savedLog) {
      try {
        setLockLog(JSON.parse(savedLog));
      } catch (e) {
        setLockLog([]);
      }
    } else {
      setLockLog([]);
    }

    setIsLoading(false);
  }, []);

  // Handle lock toggle
  const handleLockToggle = () => {
    const currentDate = getCurrentDate();
    const timestamp = getCurrentTimestamp();

    if (!isLocked) {
      // Lock the door
      setIsLocked(true);
      localStorage.setItem("lockDate", currentDate);

      // Add lock entry to log
      const newLogEntry = {
        action: "locked",
        timestamp: timestamp,
        id: Date.now(),
      };
      const updatedLog = [...lockLog, newLogEntry];
      setLockLog(updatedLog);
      localStorage.setItem(
        `lockLog_${currentDate}`,
        JSON.stringify(updatedLog)
      );
    } else {
      // Unlock (for debugging/reset purposes)
      setIsLocked(false);
      localStorage.removeItem("lockDate");

      // Add unlock entry to log
      const newLogEntry = {
        action: "unlocked",
        timestamp: timestamp,
        id: Date.now(),
      };
      const updatedLog = [...lockLog, newLogEntry];
      setLockLog(updatedLog);
      localStorage.setItem(
        `lockLog_${currentDate}`,
        JSON.stringify(updatedLog)
      );
    }
  };

  // Dynamic button styles based on state
  const getButtonStyle = () => ({
    ...styles.lockButton,
    backgroundColor: isLocked ? "#d4edda" : "#f8f9fa",
    borderColor: isLocked ? "#c3e6cb" : "#e9ecef",
  });

  if (isLoading) {
    return (
      <div style={styles.container}>
        <div style={styles.content}>
          <div style={styles.loadingText}>Loading...</div>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>Lock Reminder</title>
        <meta name="description" content="Daily lock reminder widget" />
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, user-scalable=no"
        />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div style={styles.container}>
        <div style={styles.content}>
          <button
            style={getButtonStyle()}
            onClick={handleLockToggle}
            aria-label={
              isLocked ? "Door is locked" : "Tap to confirm door is locked"
            }
          >
            <div style={styles.lockIcon}>{isLocked ? "🔒" : "🔓"}</div>
          </button>

          <div style={styles.statusText}>
            {isLocked ? (
              <>
                <span style={styles.statusEmoji}>✅</span>
                <span>You locked the door today</span>
              </>
            ) : (
              <>
                <span style={styles.statusEmoji}>❌</span>
                <span>You haven't locked it yet</span>
              </>
            )}
          </div>

          <div style={styles.dateText}>{getCurrentDate()}</div>

          {lockLog.length > 0 && (
            <div style={styles.logContainer}>
              <div style={styles.logTitle}>Today's Activity Log</div>
              <div style={styles.logList}>
                {lockLog.map((entry) => (
                  <div key={entry.id} style={styles.logEntry}>
                    <span style={styles.logAction}>
                      {entry.action === "locked" ? "🔒 Locked" : "🔓 Unlocked"}
                    </span>
                    <span style={styles.logTimestamp}>{entry.timestamp}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
}

const styles = {
  container: {
    minHeight: "100vh",
    backgroundColor: "#ffffff",
    display: "flex",
    alignItems: "flex-start",
    justifyContent: "center",
    padding: "20px 20px 40px 20px",
    fontFamily:
      '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    paddingTop: "60px",
  },
  content: {
    textAlign: "center",
    width: "100%",
    maxWidth: "420px",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
  },
  lockButton: {
    backgroundColor: "#f8f9fa",
    border: "3px solid #e9ecef",
    cursor: "pointer",
    padding: "20px",
    borderRadius: "50%",
    transition: "all 0.2s ease",
    marginBottom: "30px",
    width: "200px",
    height: "200px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    margin: "0 auto 30px auto",
    boxShadow: "0 8px 32px rgba(0, 0, 0, 0.12)",
    WebkitTapHighlightColor: "transparent",
    touchAction: "manipulation",
    userSelect: "none",
    WebkitUserSelect: "none",
    MozUserSelect: "none",
    msUserSelect: "none",
  },
  lockIcon: {
    fontSize: "80px",
    lineHeight: "1",
  },
  statusText: {
    fontSize: "24px",
    fontWeight: "600",
    color: "#333333",
    marginBottom: "20px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    gap: "10px",
  },
  statusEmoji: {
    fontSize: "28px",
  },
  dateText: {
    fontSize: "16px",
    color: "#666666",
    fontWeight: "400",
  },
  loadingText: {
    fontSize: "24px",
    color: "#666666",
  },
  logContainer: {
    marginTop: "40px",
    width: "100%",
    maxWidth: "400px",
  },
  logTitle: {
    fontSize: "18px",
    fontWeight: "600",
    color: "#333333",
    marginBottom: "15px",
    textAlign: "center",
  },
  logList: {
    display: "flex",
    flexDirection: "column",
    gap: "8px",
  },
  logEntry: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    padding: "12px 16px",
    backgroundColor: "#f8f9fa",
    borderRadius: "8px",
    border: "1px solid #e9ecef",
    fontSize: "14px",
  },
  logAction: {
    fontWeight: "500",
    color: "#333333",
  },
  logTimestamp: {
    color: "#666666",
    fontSize: "13px",
  },
};
