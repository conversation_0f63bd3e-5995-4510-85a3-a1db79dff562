import { useState, useEffect } from "react";
import Head from "next/head";

export default function LockReminder() {
  const [isLocked, setIsLocked] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Get current date in YYYY-MM-DD format
  const getCurrentDate = () => {
    const now = new Date();
    return (
      now.getFullYear() +
      "-" +
      String(now.getMonth() + 1).padStart(2, "0") +
      "-" +
      String(now.getDate()).padStart(2, "0")
    );
  };

  // Check lock status on component mount
  useEffect(() => {
    const currentDate = getCurrentDate();
    const savedDate = localStorage.getItem("lockDate");

    if (savedDate === currentDate) {
      setIsLocked(true);
    } else {
      setIsLocked(false);
      // Clear old date if it's different
      if (savedDate && savedDate !== currentDate) {
        localStorage.removeItem("lockDate");
      }
    }

    setIsLoading(false);
  }, []);

  // Handle lock toggle
  const handleLockToggle = () => {
    const currentDate = getCurrentDate();

    if (!isLocked) {
      // Lock the door
      setIsLocked(true);
      localStorage.setItem("lockDate", currentDate);
    } else {
      // Unlock (for debugging/reset purposes)
      setIsLocked(false);
      localStorage.removeItem("lockDate");
    }
  };

  // Dynamic button styles based on state
  const getButtonStyle = () => ({
    ...styles.lockButton,
    backgroundColor: isLocked ? "#d4edda" : "#f8f9fa",
    borderColor: isLocked ? "#c3e6cb" : "#e9ecef",
  });

  if (isLoading) {
    return (
      <div style={styles.container}>
        <div style={styles.content}>
          <div style={styles.loadingText}>Loading...</div>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>Lock Reminder</title>
        <meta name="description" content="Daily lock reminder widget" />
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, user-scalable=no"
        />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div style={styles.container}>
        <div style={styles.content}>
          <button
            style={getButtonStyle()}
            onClick={handleLockToggle}
            aria-label={
              isLocked ? "Door is locked" : "Tap to confirm door is locked"
            }
          >
            <div style={styles.lockIcon}>{isLocked ? "🔒" : "🔓"}</div>
          </button>

          <div style={styles.statusText}>
            {isLocked ? (
              <>
                <span style={styles.statusEmoji}>✅</span>
                <span>You locked the door today</span>
              </>
            ) : (
              <>
                <span style={styles.statusEmoji}>❌</span>
                <span>You haven't locked it yet</span>
              </>
            )}
          </div>

          <div style={styles.dateText}>{getCurrentDate()}</div>
        </div>
      </div>
    </>
  );
}

const styles = {
  container: {
    minHeight: "100vh",
    backgroundColor: "#ffffff",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    padding: "20px",
    fontFamily:
      '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  },
  content: {
    textAlign: "center",
    width: "100%",
    maxWidth: "400px",
  },
  lockButton: {
    backgroundColor: "#f8f9fa",
    border: "3px solid #e9ecef",
    cursor: "pointer",
    padding: "20px",
    borderRadius: "50%",
    transition: "all 0.2s ease",
    marginBottom: "30px",
    width: "200px",
    height: "200px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    margin: "0 auto 30px auto",
    boxShadow: "0 8px 32px rgba(0, 0, 0, 0.12)",
    WebkitTapHighlightColor: "transparent",
    touchAction: "manipulation",
    userSelect: "none",
    WebkitUserSelect: "none",
    MozUserSelect: "none",
    msUserSelect: "none",
  },
  lockIcon: {
    fontSize: "80px",
    lineHeight: "1",
  },
  statusText: {
    fontSize: "24px",
    fontWeight: "600",
    color: "#333333",
    marginBottom: "20px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    gap: "10px",
  },
  statusEmoji: {
    fontSize: "28px",
  },
  dateText: {
    fontSize: "16px",
    color: "#666666",
    fontWeight: "400",
  },
  loadingText: {
    fontSize: "24px",
    color: "#666666",
  },
};
