import styled from "styled-components";
import styles from "../styles/Home.module.css";
import { device } from "./device";
// import LinkedInLogo from '/assets/linked_in.svg';
const TitleFlex = styled.div`
  display: flex;
  width: 100%;
  justify-content: space-between;
  flex-flow: row wrap;
`;

const BiggerFlex = styled.div`
  display: flex;
  justify-content: space-between;
  flex-flow: column wrap;
  @media ${device.mobileL} {
    width: 80%;
    margin-top: 20%;
  }

  @media ${device.mobileM} {
    width: 80%;
    margin-top: 20%;
  }

  @media ${device.mobileS} {
    width: 80%;
    margin-top: 20%;
  }

  @media ${device.laptop} {
    width: 65%;
    margin-top: 7.5%;
  }

  @media ${device.desktop} {
    width: 65%;
    margin-top: 7.5%;
  }
`;

const Headline = styled.div`
  font-size: 35px;
  text-align: left;
  @media ${device.mobileL} {
    font-size: 30px;
  }

  @media ${device.mobileM} {
    font-size: 25px;
  }

  @media ${device.mobileS} {
    font-size: 20px;
  }

  @media ${device.laptop} {
    font-size: 30px;
  }

  @media ${device.desktop} {
    font-size: 30px;
  }
`;

const Paragraph = styled.div`
  text-align: left;
  width: 100%;
  padding-left: 5%;

  @media ${device.mobileL} {
    font-size: 20px;
  }

  @media ${device.mobileM} {
    font-size: 15px;
  }

  @media ${device.mobileS} {
    font-size: 15px;
  }

  @media ${device.laptop} {
    font-size: 20px;
  }

  @media ${device.desktop} {
    font-size: 20px;
  }
`;

const ListItem = styled.li`
  font-size: 15px;
  text-align: left;
  width: 100%;

  @media ${device.mobileL} {
    font-size: 20px;
  }

  @media ${device.mobileM} {
    font-size: 15px;
  }

  @media ${device.mobileS} {
    font-size: 15px;
  }

  @media ${device.laptop} {
    font-size: 20px;
  }

  @media ${device.desktop} {
    font-size: 20px;
  }
`;

const Picture = styled.img`
  width: 250px;
  height: auto;
  margin: auto;
`;

const SpecialText = styled.a`
  font-weight: bold;

  &:hover {
    color: #1c694d;
  }
`;

const ColFlex = styled.a`
  display: flex;
  flex-flow: column nowrap;
  margin: auto;
  padding-left: 5%;
  @media ${device.mobileL} {
    max-width: 100%;
  }

  @media ${device.mobileM} {
    max-width: 100%;
  }

  @media ${device.mobileS} {
    max-width: 100%;
  }

  @media ${device.laptop} {
    max-width: 60%;
  }
  @media ${device.desktop} {
    max-width: 60%;
  }
`;

const ColFlex2 = styled.a`
  display: flex;
  flex-flow: column nowrap;
  margin: auto;
  @media ${device.supersmall} {
    display: none;
  }
  @media ${device.mobileL} {
    display: none;
  }

  @media ${device.mobileM} {
    display: none;
  }

  @media ${device.mobileS} {
    display: none;
  }

  @media ${device.laptop} {
    display: flex;
  }

  @media ${device.desktop} {
    display: flex;
  }
`;

const DesktopLinksBar = styled.div`
  display: flex;
  width: 100%;
  padding: 5px;
  flex-flow: row wrap;
  justify-content: center;

  @media ${device.mobileL} {
    align-self: start;
  }

  @media ${device.mobileM} {
    align-self: start;
  }

  @media ${device.mobileS} {
    align-self: start;
  }

  @media ${device.laptop} {
    align-self: center;
  }
  @media ${device.desktop} {
    align-self: center;
  }
`;

const MobileLinksBar = styled.div`
  display: flex;
  width: 100%;
  padding: 5px;
  flex-flow: row wrap;
  justify-content: start;

  @media ${device.mobileL} {
    align-self: start;
  }

  @media ${device.mobileM} {
    align-self: start;
  }

  @media ${device.mobileS} {
    align-self: start;
  }

  @media ${device.laptop} {
    align-self: center;
    display: none;
  }
  @media ${device.desktop} {
    align-self: center;
    display: none;
  }
`;
const Icon = styled.a`
  width: 40px;
  height: auto;
  margin-right: 5%;
  &:hover {
    color: #1c694d;
  }
`;
export default function About() {
  return (
    <BiggerFlex id="about">
      <TitleFlex>
        <ColFlex2>
          <Picture src="assets/anjan-memoji.png" />
          <DesktopLinksBar>
            <Icon href="https://www.linkedin.com/in/anjanbharadwaj/">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" />
              </svg>
            </Icon>
            <Icon href="https://github.com/anjanbharadwaj">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
              </svg>
            </Icon>
            <Icon href="mailto:<EMAIL>">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                viewBox="0 0 18 18"
                fill="currentColor"
              >
                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
              </svg>
            </Icon>
            <Icon href="https://twitter.com/anjanbharadwaj_">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z" />
              </svg>
            </Icon>
          </DesktopLinksBar>
        </ColFlex2>
        <ColFlex>
          <MobileLinksBar>
            <Icon href="https://www.linkedin.com/in/anjanbharadwaj/">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" />
              </svg>
            </Icon>
            <Icon href="https://github.com/anjanbharadwaj">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
              </svg>
            </Icon>
            <Icon href="mailto:<EMAIL>">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                viewBox="0 0 18 18"
                fill="currentColor"
              >
                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
              </svg>
            </Icon>
            <Icon href="https://twitter.com/anjanbharadwaj_">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z" />
              </svg>
            </Icon>
          </MobileLinksBar>
          <h1 className={styles.title}>Hey, I'm Anjan!</h1>
          <Headline>
            I'm a Member of Technical Staff at OpenAI on the API team, based out
            of San Francisco. I previously graduated from UC Berkeley with a
            degree in computer science. I enjoy working on difficult technical
            problems with a product-oriented approach, with current interests in
            AI, infrastructure, and distributed systems.
          </Headline>
        </ColFlex>
      </TitleFlex>
      <div style={{ marginTop: "10%" }}>
        <Paragraph>
          In my free time, I enjoy:{" "}
          <ul>
            <ListItem>
              going on runs around the beautiful city of San Francisco
            </ListItem>
            <ListItem>
              singing {"("}I sang Tenor I in{" "}
              <SpecialText href="https://noteworthy.berkeley.edu/">
                Noteworthy Acappella
              </SpecialText>
              {")"}
            </ListItem>
            meeting new friends, trying new food and drink, and traveling
            whenever I get the chance
          </ul>
        </Paragraph>
        <Paragraph>
          Previously, I've:
          <ul>
            <ListItem>
              worked on building a tight knit community of developers &
              designers through{" "}
              <SpecialText href="https://webatberkeley.org/">
                Web at Berkeley
              </SpecialText>
            </ListItem>
            <ListItem>
              learned from and advised startups through UC Berkeley's homegrown
              startup accelerator,{" "}
              <SpecialText href="https://www.freeventures.org">
                Free Ventures
              </SpecialText>
            </ListItem>
            <ListItem>
              interned at{" "}
              <SpecialText href="https://convex.dev/">Convex</SpecialText>,
              where I worked on cool database and systems problems
            </ListItem>
            <ListItem>
              interned at{" "}
              <SpecialText href="https://moderntreasury.com">
                Modern Treasury
              </SpecialText>{" "}
              on the Payments Platform team, building bank-data ETL
              infrastructure and features for auditing data integrity
            </ListItem>
            <ListItem>
              interned at <SpecialText href="https://nuro.ai">Nuro</SpecialText>{" "}
              on the Ground Truth team on two occasions, (1) building core
              infrastructure to power embeddings similarity search product & (2)
              creating tools to aggregate and auto-correct bad lidar bounding
              box labels.
            </ListItem>
            <ListItem>
              interned with{" "}
              <SpecialText href="https://www.vmware.com/">VMware</SpecialText>'s
              NSX Intelligence team, building a metrics exporter layer for
              Kubernetes workloads{" "}
            </ListItem>
            <ListItem>
              helped develop{" "}
              <SpecialText href="https://cytotrace.stanford.edu/">
                Cytotrace
              </SpecialText>
              , a data-driven genomics tool to predict future biological cell
              states{" "}
              <SpecialText href="https://doi.org/10.1126/science.aax0249">
                [DOI]
              </SpecialText>
            </ListItem>
          </ul>
        </Paragraph>
      </div>
    </BiggerFlex>
  );
}
