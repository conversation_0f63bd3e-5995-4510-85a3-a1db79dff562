import styled from 'styled-components';
import styles from '../styles/Home.module.css'

const CardTitle = styled.div`
    height: auto;
    width: auto;
    font-size: 30px;
    font-weight: bold;
    // background-color:yellow;   
`

const BiggerFlex = styled.div`
    margin-top: 5%;
    display: flex; 
    background-color: grey; 
    width: 65%;
    justify-content: space-between;
    flex-flow: column wrap;
`

const Headline = styled.div`
    font-size: 30px; 
    text-align: left;
    // background-color: yellow;
    max-width: 75%; 
    padding-left: 5%;
    padding-right: 5%;  
    margin: auto;  
`

const Paragraph = styled.div`
    font-size: 30px; 
    text-align: left;
    background-color: yellow;
    width: 100%; 
    padding-left: 5%;
    padding-right: 5%; 
`

const ListItem = styled.li`
    font-size: 25px; 
    text-align: left; 
    // background-color: brown;
    width: 100%;   
`

const CardIcon = styled.a`
    width: 25px;
    height: auto;
    margin-right: 5%;
    &:hover {
        color: #1c694d;  
    }
`

const SpecialText = styled.a`
    font-weight: bold;
    
    &:hover {
        color: #1c694d; 
    }
`

const Card = styled.div`
    display: flex;
    border-style: solid; 
    border-width: 1px;
    border-color: lightgrey;
    border-radius: 10px;
    padding: 30px;
    flex-flow: row wrap;
    width: 25%;
    min-width: 400px;
    margin-left: 5%;
    margin-top: 2%;
`

const CardHeader = styled.div`
    display: flex;
    width: 100%;
    padding: 5px;
    flex-flow: row wrap;
    justify-content: space-between;
    // background-color: red; 
`

const CardLinksBar = styled.div`
    display: flex;
    width: 100%;
    padding: 5px;
    flex-flow: row wrap; 
    justify-content: start;
`

const CardBody = styled.div`
    display: flex;
    width: 100%;
    padding: 5px;
    flex-flow: row wrap;
    justify-content: space-between;
    // background-color: orange;
    font-size: 15px;
`

const CardFlexGrid = styled.div`
    margin: auto;
    display: flex;
    width: 75%;
    padding: 5px;
    flex-flow: row wrap;
    justify-content: center
`

const CardShortDetails = styled.div`
    display: flex;
    width: 100%;
    padding: 5px;
    flex-flow: row wrap;
    justify-content: space-between;
    font-size: 15px;
`
const BoldDiv = styled.div`
    font-weight: bold;
`

const StyledLink = styled.a`
    text-decoration: underline;
    &:hover {
        color: #1c694d; 
    }
`
const CardLine = styled.hr`
    border: 0;
    border-top: 1px solid lightgrey;
    border-bottom: 1px solid #fff;
    width: 100%;
`
export default function Projects() {
    return (
        <div id="projects">
        <h1 className={styles.title2}>
                        Projects
        </h1>
        <CardFlexGrid>
        <Card>
            <CardHeader>
                <CardTitle>Politiq</CardTitle>
                {/* <Logo src="assets/politiq.png"></Logo> */}
            </CardHeader>
            <CardShortDetails>
                <StyledLink href="https://webatberkeley.org/">
                Web Development at Berkeley
                </StyledLink>
                <BoldDiv>
                Fall 2021
                </BoldDiv>
            </CardShortDetails>
            <CardLine/>
            <CardBody>
                Political-tech platform that simplifies the hiring process by connecting campaigns and volunteers/staffers.
            </CardBody>
            <CardLinksBar>
                <CardIcon href="https://www.politiq.org/">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                        <path strokeLinecap="round" strokeLinejoin="round" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                </CardIcon>
                {/* <CardIcon src="assets/website.webp"></CardIcon> */}
            </CardLinksBar>
        </Card>

        <Card>
            <CardHeader>
                <CardTitle>Clicked</CardTitle>
                {/* <Logo src="assets/politiq.png"></Logo> */}
            </CardHeader>
            <CardShortDetails>
                <StyledLink href="https://webatberkeley.org/">
                Web Development at Berkeley
                </StyledLink>
                <BoldDiv>
                Spring 2021
                </BoldDiv>
            </CardShortDetails>
            <CardLine/>
            <CardBody>
                Career exploration & development platform where learners can engage with industry experts via workshops, case studies, and more.
            </CardBody>
            <CardLinksBar>
                <CardIcon href="https://www.clicked.com/">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                        <path strokeLinecap="round" strokeLinejoin="round" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                </CardIcon>
                {/* <CardIcon src="assets/website.webp"></CardIcon> */}
            </CardLinksBar>
        </Card>

        <Card>
            <CardHeader>
                <CardTitle>PurplePOV</CardTitle>
            </CardHeader>
            <CardShortDetails>
                <StyledLink href="https://www.hacksc.com/">
                HackSC
                </StyledLink>
                <BoldDiv>
                Spring 2021
                </BoldDiv>
            </CardShortDetails>
            <CardLine/>
            <CardBody>
                Social media & messaging platform aimed at reducing political polarization and news bias. Grand Prize Winner @ HackSC 2021.
            </CardBody>
            <CardLinksBar>
                <CardIcon href="https://purple-io.github.io/landing-page/">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                        <path strokeLinecap="round" strokeLinejoin="round" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                </CardIcon>
                <CardIcon href="https://github.com/Purple-io">
                    <svg xmlns="http://www.w3.org/2000/svg" role="img" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-github"><title>GitHub</title><path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path></svg>
                </CardIcon>
            </CardLinksBar>
        </Card>

        <Card>
            <CardHeader>
                <CardTitle>Berkeley Mobile</CardTitle>
            </CardHeader>
            <CardShortDetails>
                <StyledLink href="https://octo.asuc.org/">
                Berkeley Office of the CTO
                </StyledLink>
                <BoldDiv>
                2020 - 2021
                </BoldDiv>
            </CardShortDetails>
            <CardLine/>
            <CardBody>
                Official, campus-sponsored mobile application for UC Berkeley students.
            </CardBody>
            <CardLinksBar>
                <CardIcon href="https://play.google.com/store/apps/details?id=com.asuc.asucmobile&hl=en_US&gl=US">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                        <path strokeLinecap="round" strokeLinejoin="round" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                </CardIcon>
                <CardIcon href="https://github.com/asuc-octo/berkeley-mobile-android-persona">
                                        <svg xmlns="http://www.w3.org/2000/svg" role="img" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-github"><title>GitHub</title><path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path></svg>

                </CardIcon>
            </CardLinksBar>
        </Card>

        <Card>
            <CardHeader>
                <CardTitle>Facebook Reminders</CardTitle>
            </CardHeader>
            <CardShortDetails>
                <div>
                Personal Project
                </div>
                <BoldDiv>
                Summer 2020
                </BoldDiv>
            </CardShortDetails>
            <CardLine/>
            <CardBody>
                Bringing chat reminders back (unofficially) to Facebook Messenger.
            </CardBody>
            <CardLinksBar>
                <CardIcon href="https://github.com/anjanbharadwaj/fb-reminders">
                    <svg xmlns="http://www.w3.org/2000/svg" role="img" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-github"><title>GitHub</title><path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path></svg>
                </CardIcon>
            </CardLinksBar>
        </Card>

        <Card>
            <CardHeader>
                <CardTitle>DaPiano</CardTitle>
            </CardHeader>
            <CardShortDetails>
                <div>
                Personal Project
                </div>
                <BoldDiv>
                Summer 2021
                </BoldDiv>
            </CardShortDetails>
            <CardLine/>
            <CardBody>
                A piano full of DaBaby sounds. Mostly for personal entertainment.
            </CardBody>
            <CardLinksBar>
                <CardIcon href="https://dapiano.vercel.app/">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                        <path strokeLinecap="round" strokeLinejoin="round" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                </CardIcon>
                <CardIcon href="https://github.com/anjanbharadwaj/DaWebsite">
                                        <svg xmlns="http://www.w3.org/2000/svg" role="img" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-github"><title>GitHub</title><path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path></svg>

                </CardIcon>
            </CardLinksBar>
        </Card>

        <Card>
            <CardHeader>
                <CardTitle>Comparateor</CardTitle>
            </CardHeader>
            <CardShortDetails>
                <div>
                Personal Project
                </div>
                <BoldDiv>
                Winter 2022
                </BoldDiv>
            </CardShortDetails>
            <CardLine/>
            <CardBody>
                I'd say this is r/applyingtocollege but better. Or maybe more cringe.
            </CardBody>
            <CardLinksBar>
                <CardIcon href="https://comparateor.vercel.app/">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                        <path strokeLinecap="round" strokeLinejoin="round" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                </CardIcon>
            </CardLinksBar>
        </Card>
        </CardFlexGrid>
        </div>
    );
}





