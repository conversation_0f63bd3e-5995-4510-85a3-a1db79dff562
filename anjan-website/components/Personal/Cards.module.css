.cards-container {
  position: relative;
  width: 20px;
  height: 450px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  perspective: 1500px;
}

.cards-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  width: 100%;
  padding: 20px;
}

.card-wrapper {
  position: absolute;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  will-change: transform;
}

.card {
  position: relative;
  width: 20px;
  height: auto;
  border-radius: 15px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  will-change: transform;
  box-shadow: 0 12px 25px rgba(0, 0, 0, 0.2);
  cursor: grab;
  touch-action: none;
  overflow: hidden;
}

.card:active {
  cursor: grabbing;
}

.card-content {
  position: absolute;
  bottom: 0;
  width: 20px;
  height: 100%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  padding: 20px;
  color: white;
  border-bottom-left-radius: 15px;
  border-bottom-right-radius: 15px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.card-title {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 8px;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

.card-ability {
  font-size: 0.9rem;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}