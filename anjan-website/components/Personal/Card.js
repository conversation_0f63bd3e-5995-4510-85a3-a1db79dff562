import React, { useState, useRef, useEffect } from 'react';
import '../styles/Card.css';

function Card({ card, zIndex, onRemove }) {
  const [isDragging, setIsDragging] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [startPos, setStartPos] = useState({ x: 0, y: 0 });
  
  const cardRef = useRef(null);

  const handleMouseDown = (e) => {
    setIsDragging(true);
    setStartPos({ 
      x: e.clientX - position.x, 
      y: e.clientY - position.y 
    });
  };

  const handleTouchStart = (e) => {
    setIsDragging(true);
    setStartPos({ 
      x: e.touches[0].clientX - position.x, 
      y: e.touches[0].clientY - position.y 
    });
  };

  const handleMouseMove = (e) => {
    if (!isDragging) return;
    
    const newX = e.clientX - startPos.x;
    const newY = e.clientY - startPos.y;
    
    setPosition({ x: newX, y: newY });
  };

  const handleTouchMove = (e) => {
    if (!isDragging) return;
    
    const newX = e.touches[0].clientX - startPos.x;
    const newY = e.touches[0].clientY - startPos.y;
    
    setPosition({ x: newX, y: newY });
  };

  const handleDragEnd = () => {
    setIsDragging(false);
    
    // If swiped far enough, remove the card
    if (Math.abs(position.x) > 100) {
      onRemove(card.id);
    } else {
      // Return to original position
      setPosition({ x: 0, y: 0 });
    }
  };

  useEffect(() => {
    const currentCard = cardRef.current;
    
    if (currentCard) {
      currentCard.addEventListener('touchmove', handleTouchMove);
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleDragEnd);
      document.addEventListener('touchend', handleDragEnd);
    }
    
    return () => {
      if (currentCard) {
        currentCard.removeEventListener('touchmove', handleTouchMove);
      }
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleDragEnd);
      document.removeEventListener('touchend', handleDragEnd);
    };
  }, [isDragging, startPos]);

  const rotate = position.x * 0.1; // Rotation effect
  const cardStyle = {
    transform: `translate(${position.x}px, ${position.y}px) rotate(${rotate}deg)`,
    zIndex: zIndex,
    transition: isDragging ? 'none' : 'transform 0.5s',
    cursor: isDragging ? 'grabbing' : 'grab'
  };

  return (
    <div 
      className="card" 
      style={cardStyle}
      ref={cardRef}
      onMouseDown={handleMouseDown}
      onTouchStart={handleTouchStart}
    >
      <div className="card-content">
        <img className="card-image" src={card.image} alt={card.title} />
        <div className="card-info">
          <h2 className="card-title">{card.title}</h2>
          <p className="card-ability">{card.ability}</p>
        </div>
      </div>
    </div>
  );
}

export default Card;