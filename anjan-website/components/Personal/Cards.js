
import React, { useState } from 'react';
import { useSprings, animated, to as interpolate } from '@react-spring/web';
import { useDrag } from '@use-gesture/react';
import './Cards.module.css';
import styles from './Cards.module.css';

// Helper functions
const to = (i) => ({
  x: 0,
  y: 0,
  scale: 1,
  rot: -2.5 + Math.random() * 5,
  delay: i * 100,
});

const from = () => ({ x: 0, rot: 0, scale: 1.5, y: -1000 });

const trans = (r, s) => `perspective(20500px) rotateX(10deg) rotateY(${r / 10}deg) rotateZ(${r}deg) scale(${s})`;

function Card(props) {
    return (
        <div className="card">
            <div className="card-content">
                <img className="card-image" height="400px" src={"/assets/"+props.url} alt={props.title} />
                <div className="card-info">
                <h2 className="card-title">{props.title}</h2>
                {/* <p className="card-ability">{props.ability}</p> */}
                </div>
            </div>
        </div>
    );
}
function Cards() {
  const cards = [
    {
      id: 1,
      title: "GitLit",
      url: "gitlit.png",
      ability: "We went to prom. Twice!",
      orientation: 'portrait',
      color: "#3d3938"
    },
    {
      id: 2,
      title: "Gas Works Park",
      url: "seattle.png",
      ability: "Lounging around on a beautiful day in your hometown",
      orientation: 'portrait',
      color: "#e09563"
    },
    {
      id: 3,
      title: "6 Months",
      url: "boat.png",
      ability: "We went boating and you definitely did all the work",
      orientation: 'portrait',
      color: "#807173"
    },
    {
      id: 4,
      title: "Barcelona",
      url: "barca.png",
      ability: "Mesmerized by the Sagrada Familia",
      orientation: 'portrait',
      color: "#d8aa60"
    },
    {
      id: 5,
      title: "Seville",
      url: "seville.png",
      ability: "Ate some yummy food and saw a lot of cathedrals",
      orientation: 'portrait', color: "#6c8453"
    },
    { 
      id: 6,
      title: "Sintra",
      url: "sintra.png",
      ability: "Explored real life castles for a real life princess",
      orientation: 'portrait', color: "#46859e"
    },
    {
      id: 7,
      title: "Lisbon",
      url: "lisboa.png",
      ability: "Lisboa? Lisbon? You choose.",
      orientation: 'portrait', color: "#935c31"
    }
  ];

  const [gone] = useState(() => new Set()); // Set of cards that are flicked out
  const [props, api] = useSprings(cards.length, i => ({
    ...to(i),
    from: from(i)
  }));

  // Create a drag gesture handler
  const bind = useDrag(({ args: [index], active, movement: [mx], direction: [xDir], velocity: [vx] }) => {
    const trigger = vx > 0.2; // Trigger if flicked hard enough
    if (!active && trigger) gone.add(index); // Flag card to fly out if trigger met
    
    api.start(i => {
      if (index !== i) return; // Only change data for current spring
      const isGone = gone.has(index);
      const x = isGone ? (200 + window.innerWidth) * xDir : active ? mx : 0;
      const rot = mx / 10 + (isGone ? xDir * 10 * vx : 0);
      const scale = active ? 1.1 : 1;
      
      return {
        x,
        rot,
        scale,
        delay: undefined,
        config: { friction: 50, tension: active ? 800 : isGone ? 200 : 500 }
      };
    });
    
    // Reset deck when all cards are gone
    if (!active && gone.size === cards.length) {
      setTimeout(() => {
        gone.clear();
        api.start(i => to(i));
      }, 600);
    }
  });

  return (
  <div className={styles['cards-page']}>
    <h1 className={styles['cards-title']}>Surbhi's Card Collection</h1>
    <div className={styles["cards-container"]}>
      {props.map(({ x, y, rot, scale }, i) => (
        <animated.div 
          key={cards[i].id} 
          style={{ x, y: 50, width: "500px", display: "flex", justifyContent: "center", position: 'absolute', zIndex: cards.length - i,}} 
          className="card-wrapper"
        >
          <animated.div
            {...bind(i)}
            style={{
              transform: interpolate([rot, scale], trans),
              backgroundColor: cards[i].color,
              padding: "10px",
            }}
            className="card"
          >
            <Card {...cards[i]}/>
          </animated.div>
        </animated.div>
      ))}
    </div>
    </div>
  );
}

export default Cards;