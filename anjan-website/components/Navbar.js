import styled from 'styled-components';

const NavLink = styled.a`
    display: inline-block;
    justify-content: middle;
    align: center;
    padding: 20px 20px; 
    margin-top: 0.5%;
    margin-bottom: 0.5%; 
    &:hover {  
        color: #1c694d; 
    }

    @media (max-width: 450px) {
        padding: 10px 10px; 
    }
`

const Nav = styled.div`
    position: fixed;
    top: 0; 
    align: center; 
    width: 100%; 
    justify-self: center; 
    text-align: right;
    background-color: #f2f2f2;
`

export default function Navbar() {
    return (
        <Nav>
            
            <NavLink href="/#">01. about</NavLink>
            {/* <NavLink href="#experiences">02. stuff i've done</NavLink> */}
            <NavLink href="/#projects">02. projects</NavLink>
            <NavLink href="/assets/AnjanBharadwajResume202324.pdf">03. resume</NavLink>
            <NavLink href="https://anjan.motif.land">04. blog</NavLink>
        </Nav>
    );
}